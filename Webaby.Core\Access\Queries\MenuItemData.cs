﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Webaby.Web;

namespace Webaby.Core.Access.Queries
{
    public class MenuItemData: ITreeNode
    {
        public Guid Id { get; set; }

        public Guid? BusinessPermissionId { get; set; }

        public Guid? ParentId { get; set; }

        public string Title { get; set; }

        public string ActionName { get; set; }

        public string ControllerName { get; set; }

        public string MenuClass { get; set; }

        public bool IsSection { get; set; }

        public int DisplayOrder { get; set; }
    }
}

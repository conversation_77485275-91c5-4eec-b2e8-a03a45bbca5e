﻿using System;
using System.Data.Linq.Mapping;
using Webaby.Data;

namespace Webaby.Core.UserAccount
{
    [Table(Name = "dbo.UserProfiles")]
    public class UserProfileEntity : Entity
    {
        [Column(IsPrimaryKey = true)]
        public override Guid Id { get; set; }

        [Column]
        public Guid? OrganizationId { get; set; }

        [Column]
        public string FullName { get; set; }

        [Column]
        public string PhoneNumber { get; set; }

        [Column]
        public string Email { get; set; }

        [Column]
        public bool IsWebApiUserAccount { get; set; }

        [Column]
        public string BusinessRole { get; set; }

        [Column]
        public string AvayaAgentID { get; set; }

        [Column]
        public string AvayaStationID { get; set; }

        [Column]
        public string AvayaAgentPassword { get; set; }

        [Column]
        public string PCStaticIP { get; set; }

        [Column]
        public string VideoDeviceStaticIP { get; set; }

        [Column]
        public string VideoDeviceStaticPort { get; set; }

        [Column]
        public bool IsCUStation { get; set; }

        [Column]
        public string LocationId { get; set; }

        [Column]
        public string eOfficeId { get; set; }

        [Column]
        public bool? eOfficeEnabled { get; set; }

        [Column]
        public Guid? PictureId { get; set; }

        [Column]
        public string AgentCode { get; set; }

        [Column]
        public string Extension { get; set; }

        [Column]
        public int? CompetenceLevel { get; set; }
    }
}
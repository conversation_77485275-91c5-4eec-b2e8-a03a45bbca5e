﻿using System;
using System.Data.Linq.Mapping;
using Webaby.Data;

namespace Webaby.Core.UserAccount
{
    [Table(Name = "dbo.aspnet_Users")]
    public class UserEntity : Entity
    {
        [Column]
        public Guid ApplicationId { get; set; }

        [Column(IsPrimaryKey = true, Name = "UserId")]
        public override Guid Id { get; set; }

        [Column]
        public String UserName { get; set; }
    }
}

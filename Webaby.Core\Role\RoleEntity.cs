﻿using System;
using System.Data.Linq.Mapping;
using Webaby;
using Webaby.Data;

namespace Webaby.Core.Role
{
    [Table(Name = "[Roles]")]
    public class RoleEntity : Entity
    {
        [Column(IsPrimaryKey = true, Name = "Id")]
        public override Guid Id { get; set; }

        [Rule("Required")]
        [Column]
        public string Name { get; set; }

        [Column]
        public Int64 Permissions { get; set; }
    }
}
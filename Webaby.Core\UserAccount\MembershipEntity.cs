﻿using System;
using System.Data.Linq.Mapping;
using Webaby.Data;

namespace Webaby.Core.UserAccount
{
    [Table(Name = "dbo.aspnet_Membership")]
    public class MembershipEntity : Entity
    {
        [Column]
        public Guid ApplicationId { get; set; }

        [Column(IsPrimaryKey = true, Name = "UserId")]
        public override Guid Id { get; set; }

        [Column]
        public String Password { get; set; }

        [Column]
        public String PasswordFormat { get; set; }

        [Column]
        public String PasswordSalt { get; set; }

        [Column]
        public String Email { get; set; }

        [Column]
        public Boolean IsApproved { get; set; }
    }
}